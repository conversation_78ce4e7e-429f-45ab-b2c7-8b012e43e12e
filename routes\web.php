<?php
/**
 * Web Routes for JobSpace Application
 * Public routes for non-authenticated users
 */

// Public Pages Routes
$router->get('/', 'PublicController@home');
$router->get('/home', 'PublicController@home');
$router->get('/about', 'PublicController@about');
$router->get('/contact', 'PublicController@contact');
$router->get('/help', 'PublicController@help');
$router->get('/faq', 'PublicController@faq');
$router->get('/privacy', 'PublicController@privacy');
$router->get('/terms', 'PublicController@terms');
$router->get('/community', 'PublicController@community');

// Job Related Routes
$router->get('/jobs', 'JobController@index');
$router->get('/jobs/{id}', 'JobController@show');
$router->get('/companies', 'CompanyController@index');
$router->get('/companies/{id}', 'CompanyController@show');

// Authentication Routes
$router->get('/auth/login', 'AuthController@showLogin');
$router->post('/auth/login', 'AuthController@login');
$router->get('/auth/register', 'AuthController@showRegister');
$router->post('/auth/register', 'AuthController@register');
$router->get('/auth/logout', 'AuthController@logout');
$router->get('/auth/forgot-password', 'AuthController@showForgotPassword');
$router->post('/auth/forgot-password', 'AuthController@forgotPassword');
$router->get('/auth/reset-password', 'AuthController@showResetPassword');
$router->post('/auth/reset-password', 'AuthController@resetPassword');
$router->get('/auth/verify-code', 'AuthController@showVerifyCode');
$router->post('/auth/verify-code', 'AuthController@verifyCode');

// API Routes for AJAX requests
$router->post('/api/newsletter/subscribe', 'ApiController@subscribeNewsletter');
$router->post('/api/contact/send', 'ApiController@sendContactMessage');
$router->get('/api/jobs/search', 'ApiController@searchJobs');
$router->get('/api/companies/search', 'ApiController@searchCompanies');

// Sitemap and SEO
$router->get('/sitemap.xml', 'SeoController@sitemap');
$router->get('/robots.txt', 'SeoController@robots');